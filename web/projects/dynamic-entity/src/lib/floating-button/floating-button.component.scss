/* Floating <PERSON><PERSON> Styles */
.floating-button {
  position: fixed;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  z-index: 1000;
  transition: all 0.3s ease;

  &:hover {
    background-color: #0056b3;
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
    transform: translateY(-50%) scale(1.05);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }

  &:focus {
    outline: none;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3),
      0 0 0 3px rgba(0, 123, 255, 0.2);
  }
}

/* Popup Backdrop */
.popup-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

/* Popup Content */
.popup-content {
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 0;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Close Button */
.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  z-index: 10;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e9ecef;
    color: #495057;
  }

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
  }
}

/* Content Area */
.content-area {
  flex: 1;
  padding: 80px 20px 20px 20px;
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .floating-button {
    left: 15px;
    width: 50px;
    height: 50px;
    font-size: 18px;
  }

  .close-button {
    top: 15px;
    right: 15px;
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .content-area {
    padding: 70px 15px 15px 15px;
  }
}
