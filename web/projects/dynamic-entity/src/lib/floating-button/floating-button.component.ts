import { Component, OnInit } from "@angular/core";
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from "@angular/animations";

@Component({
  selector: "lib-floating-button",
  templateUrl: "./floating-button.component.html",
  styleUrls: ["./floating-button.component.scss"],
  animations: [
    trigger("slideUp", [
      state(
        "closed",
        style({
          transform: "translateY(100%)",
          opacity: 0,
        })
      ),
      state(
        "open",
        style({
          transform: "translateY(0)",
          opacity: 1,
        })
      ),
      transition("closed => open", [
        animate("300ms cubic-bezier(0.25, 0.8, 0.25, 1)"),
      ]),
      transition("open => closed", [
        animate("250ms cubic-bezier(0.25, 0.8, 0.25, 1)"),
      ]),
    ]),
    trigger("backdrop", [
      state(
        "closed",
        style({
          opacity: 0,
        })
      ),
      state(
        "open",
        style({
          opacity: 1,
        })
      ),
      transition("closed => open", [animate("200ms ease-in")]),
      transition("open => closed", [animate("150ms ease-out")]),
    ]),
  ],
})
export class FloatingButtonComponent implements OnInit {
  isPopupOpen = false;

  constructor() {}

  ngOnInit(): void {}

  openPopup(): void {
    this.isPopupOpen = true;
  }

  closePopup(): void {
    this.isPopupOpen = false;
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.closePopup();
    }
  }
}
