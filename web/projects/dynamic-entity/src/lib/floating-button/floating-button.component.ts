import { Component } from "@angular/core";
import { trigger, style, transition, animate } from "@angular/animations";

@Component({
  selector: "lib-floating-button",
  templateUrl: "./floating-button.component.html",
  styleUrls: ["./floating-button.component.scss"],
  animations: [
    trigger("slideUp", [
      transition(":enter", [
        style({
          transform: "translateY(100%)",
          opacity: 0,
        }),
        animate(
          "300ms cubic-bezier(0.25, 0.8, 0.25, 1)",
          style({
            transform: "translateY(0)",
            opacity: 1,
          })
        ),
      ]),
      transition(":leave", [
        animate(
          "250ms cubic-bezier(0.25, 0.8, 0.25, 1)",
          style({
            transform: "translateY(100%)",
            opacity: 0,
          })
        ),
      ]),
    ]),
    trigger("backdrop", [
      transition(":enter", [
        style({ opacity: 0 }),
        animate("200ms ease-in", style({ opacity: 1 })),
      ]),
      transition(":leave", [animate("150ms ease-out", style({ opacity: 0 }))]),
    ]),
  ],
})
export class FloatingButtonComponent {
  isPopupOpen = false;

  openPopup(): void {
    this.isPopupOpen = true;
  }

  closePopup(): void {
    this.isPopupOpen = false;
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.closePopup();
    }
  }
}
